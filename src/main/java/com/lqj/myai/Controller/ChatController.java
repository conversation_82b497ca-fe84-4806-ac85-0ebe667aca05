package com.lqj.myai.Controller;

import com.lqj.myai.repository.ChatHistoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@CrossOrigin("*") // 允许跨域访问
@RequiredArgsConstructor // 自动注入有参构造
@RestController // 返回json数据
@RequestMapping("/ai") // 请求映射
public class ChatController {

    private final ChatClient chatClient;

    private final ChatHistoryRepository chatHistoryRepository;// 会话历史记录仓库

    // 同步调用需要等待很长时间页面才能看到结果，用户体验不好。为了解决这个问题，我们可以改进调用方式为流式调用。
    //在SpringAI中使用了WebFlux技术实现流式调用。
    @RequestMapping(value = "/chat",produces = "text/html;charset=UTF-8")
    public Flux<String> chat(@RequestParam(defaultValue = "讲个笑话") String prompt, String chatId) {
        // 保存会话记录
        chatHistoryRepository.save("chat",chatId);

        return chatClient
                .prompt(prompt)
                .advisors(a-> {
                    // 传递chatId给Advisor的方式是通过AdvisorContext，也就是以key-value形式存入上下文
                    // 其中的CHAT_MEMORY_CONVERSATION_ID_KEY是AbstractChatMemoryAdvisor中定义的常量key，将来MessageChatMemoryAdvisor执行的过程中就可以拿到这个chatId了。
                    a.param(AbstractChatMemoryAdvisor.CHAT_MEMORY_CONVERSATION_ID_KEY,chatId);
                })
                // 注意，基于call()方法的调用属于同步调用，需要所有响应结果全部返回后才能返回给前端。
                .stream()
                .content();
    }

}
