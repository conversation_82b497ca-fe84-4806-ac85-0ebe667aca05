package com.lqj.myai.repository;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@RequiredArgsConstructor
public class InMemoryChatHistoryRepository implements ChatHistoryRepository {

    // 会话历史，key为会话类型，value为会话ID列表
    private Map<String, List<String>> chatHistory = new HashMap<>();

    @Override
    public void save(String type, String chatId) {
//       目前我们业务比较简单，没有用户概念，但是将来会有不同业务，因此简单采用内存保存type与chatId关系。
//       将来大家也可以根据业务需要把会话id持久化保存到Redis、MongoDB、MySQL等数据库。
//       如果业务中有user的概念，还需要记录userId、chatId、time等关联关系
        /*if (!chatHistory.containsKey(type)) {
            chatHistory.put(type, new ArrayList<>());
        }
        List<String> chatIds = chatHistory.get(type);*/
        // 如果会话历史不存在则创建一个空的列表
        List<String> chatIds = chatHistory.computeIfAbsent(type, k -> new ArrayList<>());
        // 如果会话历史中已经存在该会话，则直接返回，避免重复添加
        if (chatIds.contains(chatId)) {
            return;
        }
        // 将会话ID添加到会话历史中
        chatIds.add(chatId);
    }

    @Override
    public List<String> getChatIds(String type) {
        /*List<String> chatIds = chatHistory.get(type);
        return chatIds == null ? List.of() : chatIds;*/
        return chatHistory.getOrDefault(type, List.of());
    }
}

