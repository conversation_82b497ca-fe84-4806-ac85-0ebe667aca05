spring:
  application:
    name: my-ai
  ai:
    ollama:
      base-url: http://localhost:11434
      chat:
        model: deepseek-r1:7b
        options:
          temperature: 0.8 # 模型温度，影响模型生成结果的随机性，越小越稳定
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: qwen-max-latest
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456

logging:
  level:
    org.springframework.ai.chat.client.advisor: debug # 将ai对话的环绕增强的日志级别设置为 DEBUG
    com.lqj.myai: debug # 将当前项目的日志级别设置为 DEBUG

